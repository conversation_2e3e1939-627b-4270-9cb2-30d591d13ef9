// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		C47E4C9D1CAA986900DF6D73 /* ARS.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C031CAA986900DF6D73 /* ARS.cpp */; };
		C47E4C9E1CAA986900DF6D73 /* AscGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C051CAA986900DF6D73 /* AscGrid.cpp */; };
		C47E4C9F1CAA986900DF6D73 /* BasicConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C071CAA986900DF6D73 /* BasicConfigSection.cpp */; };
		C47E4CA01CAA986900DF6D73 /* BasicGrids.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C091CAA986900DF6D73 /* BasicGrids.cpp */; };
		C47E4CA11CAA986900DF6D73 /* BasinConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C0B1CAA986900DF6D73 /* BasinConfigSection.cpp */; };
		C47E4CA31CAA986900DF6D73 /* BifGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C0E1CAA986900DF6D73 /* BifGrid.cpp */; };
		C47E4CA41CAA986900DF6D73 /* CaliParamConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C121CAA986900DF6D73 /* CaliParamConfigSection.cpp */; };
		C47E4CA61CAA986900DF6D73 /* Config.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C151CAA986900DF6D73 /* Config.cpp */; };
		C47E4CA71CAA986900DF6D73 /* CRESTModel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C181CAA986900DF6D73 /* CRESTModel.cpp */; };
		C47E4CA81CAA986900DF6D73 /* DamConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C1A1CAA986900DF6D73 /* DamConfigSection.cpp */; };
		C47E4CA91CAA986900DF6D73 /* DatedName.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C1C1CAA986900DF6D73 /* DatedName.cpp */; };
		C47E4CAA1CAA986900DF6D73 /* DEMProcessor.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C1F1CAA986900DF6D73 /* DEMProcessor.cpp */; };
		C47E4CAB1CAA986900DF6D73 /* DistancePerTimeUnits.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C211CAA986900DF6D73 /* DistancePerTimeUnits.cpp */; };
		C47E4CAC1CAA986900DF6D73 /* DistanceUnit.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C231CAA986900DF6D73 /* DistanceUnit.cpp */; };
		C47E4CAD1CAA986900DF6D73 /* dream_functions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C251CAA986900DF6D73 /* dream_functions.cpp */; };
		C47E4CAE1CAA986900DF6D73 /* DREAM.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C281CAA986900DF6D73 /* DREAM.cpp */; };
		C47E4CAF1CAA986900DF6D73 /* EF5.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C2A1CAA986900DF6D73 /* EF5.cpp */; };
		C47E4CB11CAA986900DF6D73 /* EnsTaskConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C2F1CAA986900DF6D73 /* EnsTaskConfigSection.cpp */; };
		C47E4CB21CAA986900DF6D73 /* ExecuteConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C311CAA986900DF6D73 /* ExecuteConfigSection.cpp */; };
		C47E4CB31CAA986900DF6D73 /* ExecutionController.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C331CAA986900DF6D73 /* ExecutionController.cpp */; };
		C47E4CB41CAA986900DF6D73 /* GaugeConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C351CAA986900DF6D73 /* GaugeConfigSection.cpp */; };
		C47E4CB51CAA986900DF6D73 /* GaugeMap.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C371CAA986900DF6D73 /* GaugeMap.cpp */; };
		C47E4CB61CAA986900DF6D73 /* GeographicProjection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C391CAA986900DF6D73 /* GeographicProjection.cpp */; };
		C47E4CB71CAA986900DF6D73 /* GriddedOutput.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C3C1CAA986900DF6D73 /* GriddedOutput.cpp */; };
		C47E4CB81CAA986900DF6D73 /* GridWriter.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C3F1CAA986900DF6D73 /* GridWriter.cpp */; };
		C47E4CB91CAA986900DF6D73 /* GridWriterFull.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C411CAA986900DF6D73 /* GridWriterFull.cpp */; };
		C47E4CBA1CAA986900DF6D73 /* HPModel.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C431CAA986900DF6D73 /* HPModel.cpp */; };
		C47E4CBB1CAA986900DF6D73 /* HyMOD.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C451CAA986900DF6D73 /* HyMOD.cpp */; };
		C47E4CBC1CAA986900DF6D73 /* InundationCaliParamConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C471CAA986900DF6D73 /* InundationCaliParamConfigSection.cpp */; };
		C47E4CBD1CAA986900DF6D73 /* InundationParamSetConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C491CAA986900DF6D73 /* InundationParamSetConfigSection.cpp */; };
		C47E4CBE1CAA986900DF6D73 /* KinematicRoute.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C4B1CAA986900DF6D73 /* KinematicRoute.cpp */; };
		C47E4CC01CAA986900DF6D73 /* LAEAProjection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C4E1CAA986900DF6D73 /* LAEAProjection.cpp */; };
		C47E4CC11CAA986900DF6D73 /* LinearRoute.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C501CAA986900DF6D73 /* LinearRoute.cpp */; };
		C47E4CC21CAA986900DF6D73 /* misc_functions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C531CAA986900DF6D73 /* misc_functions.cpp */; };
		C47E4CC31CAA986900DF6D73 /* Model.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C551CAA986900DF6D73 /* Model.cpp */; };
		C47E4CC51CAA986900DF6D73 /* MRMSGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C5A1CAA986900DF6D73 /* MRMSGrid.cpp */; };
		C47E4CC71CAA986900DF6D73 /* ObjectiveFunc.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C5D1CAA986900DF6D73 /* ObjectiveFunc.cpp */; };
		C47E4CC81CAA986900DF6D73 /* ParamSetConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C5F1CAA986900DF6D73 /* ParamSetConfigSection.cpp */; };
		C47E4CC91CAA986900DF6D73 /* PETConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C611CAA986900DF6D73 /* PETConfigSection.cpp */; };
		C47E4CCA1CAA986900DF6D73 /* PETReader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C631CAA986900DF6D73 /* PETReader.cpp */; };
		C47E4CCB1CAA986900DF6D73 /* PETType.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C651CAA986900DF6D73 /* PETType.cpp */; };
		C47E4CCC1CAA986900DF6D73 /* PrecipConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C671CAA986900DF6D73 /* PrecipConfigSection.cpp */; };
		C47E4CCD1CAA986900DF6D73 /* PrecipReader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C691CAA986900DF6D73 /* PrecipReader.cpp */; };
		C47E4CCE1CAA986900DF6D73 /* PrecipType.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C6B1CAA986900DF6D73 /* PrecipType.cpp */; };
		C47E4CCF1CAA986900DF6D73 /* RoutingCaliParamConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C6E1CAA986900DF6D73 /* RoutingCaliParamConfigSection.cpp */; };
		C47E4CD01CAA986900DF6D73 /* RoutingParamSetConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C701CAA986900DF6D73 /* RoutingParamSetConfigSection.cpp */; };
		C47E4CD11CAA986900DF6D73 /* RPSkewness.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C721CAA986900DF6D73 /* RPSkewness.cpp */; };
		C47E4CD21CAA986900DF6D73 /* SAC.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C741CAA986900DF6D73 /* SAC.cpp */; };
		C47E4CD41CAA986900DF6D73 /* SimpleInundation.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C781CAA986900DF6D73 /* SimpleInundation.cpp */; };
		C47E4CD51CAA986900DF6D73 /* Simulator.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C7A1CAA986900DF6D73 /* Simulator.cpp */; };
		C47E4CD61CAA986900DF6D73 /* Snow17Model.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C7C1CAA986900DF6D73 /* Snow17Model.cpp */; };
		C47E4CD71CAA986900DF6D73 /* SnowCaliParamConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C7E1CAA986900DF6D73 /* SnowCaliParamConfigSection.cpp */; };
		C47E4CD81CAA986900DF6D73 /* SnowParamSetConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C801CAA986900DF6D73 /* SnowParamSetConfigSection.cpp */; };
		C47E4CD91CAA986900DF6D73 /* TaskConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C821CAA986900DF6D73 /* TaskConfigSection.cpp */; };
		C47E4CDA1CAA986900DF6D73 /* TempConfigSection.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C841CAA986900DF6D73 /* TempConfigSection.cpp */; };
		C47E4CDB1CAA986900DF6D73 /* TempReader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C861CAA986900DF6D73 /* TempReader.cpp */; };
		C47E4CDC1CAA986900DF6D73 /* TempType.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C881CAA986900DF6D73 /* TempType.cpp */; };
		C47E4CDD1CAA986900DF6D73 /* TifGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C8A1CAA986900DF6D73 /* TifGrid.cpp */; };
		C47E4CDE1CAA986900DF6D73 /* TimeSeries.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C8C1CAA986900DF6D73 /* TimeSeries.cpp */; };
		C47E4CDF1CAA986900DF6D73 /* TimeUnit.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C8E1CAA986900DF6D73 /* TimeUnit.cpp */; };
		C47E4CE01CAA986900DF6D73 /* TimeVar.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C901CAA986900DF6D73 /* TimeVar.cpp */; };
		C47E4CE21CAA986900DF6D73 /* TRMMDGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C931CAA986900DF6D73 /* TRMMDGrid.cpp */; };
		C47E4CE41CAA986900DF6D73 /* TRMMRTGrid.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C961CAA986900DF6D73 /* TRMMRTGrid.cpp */; };
		C47E4CE71CAA986900DF6D73 /* VCInundation.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C47E4C9B1CAA986900DF6D73 /* VCInundation.cpp */; };
		C47E4CE91CAAAB7100DF6D73 /* UnixImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C47E4CE81CAAAB7100DF6D73 /* UnixImageIO.framework */; };
		C47E4CEB1CAAAB8000DF6D73 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C47E4CEA1CAAAB8000DF6D73 /* libz.tbd */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		C47E4BF71CAA984C00DF6D73 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = /usr/share/man/man1/;
			dstSubfolderSpec = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 1;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		C47E4BF91CAA984C00DF6D73 /* EF5 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = EF5; sourceTree = BUILT_PRODUCTS_DIR; };
		C47E4C031CAA986900DF6D73 /* ARS.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = ARS.cpp; path = ../src/ARS.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C041CAA986900DF6D73 /* ARS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ARS.h; path = ../src/ARS.h; sourceTree = SOURCE_ROOT; };
		C47E4C051CAA986900DF6D73 /* AscGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = AscGrid.cpp; path = ../src/AscGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C061CAA986900DF6D73 /* AscGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AscGrid.h; path = ../src/AscGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C071CAA986900DF6D73 /* BasicConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = BasicConfigSection.cpp; path = ../src/BasicConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C081CAA986900DF6D73 /* BasicConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BasicConfigSection.h; path = ../src/BasicConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C091CAA986900DF6D73 /* BasicGrids.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = BasicGrids.cpp; path = ../src/BasicGrids.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C0A1CAA986900DF6D73 /* BasicGrids.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BasicGrids.h; path = ../src/BasicGrids.h; sourceTree = SOURCE_ROOT; };
		C47E4C0B1CAA986900DF6D73 /* BasinConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = BasinConfigSection.cpp; path = ../src/BasinConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C0C1CAA986900DF6D73 /* BasinConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BasinConfigSection.h; path = ../src/BasinConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C0E1CAA986900DF6D73 /* BifGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = BifGrid.cpp; path = ../src/BifGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C0F1CAA986900DF6D73 /* BifGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BifGrid.h; path = ../src/BifGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C101CAA986900DF6D73 /* BoundingBox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BoundingBox.h; path = ../src/BoundingBox.h; sourceTree = SOURCE_ROOT; };
		C47E4C111CAA986900DF6D73 /* Calibrate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Calibrate.h; path = ../src/Calibrate.h; sourceTree = SOURCE_ROOT; };
		C47E4C121CAA986900DF6D73 /* CaliParamConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = CaliParamConfigSection.cpp; path = ../src/CaliParamConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C131CAA986900DF6D73 /* CaliParamConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CaliParamConfigSection.h; path = ../src/CaliParamConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C151CAA986900DF6D73 /* Config.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Config.cpp; path = ../src/Config.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C161CAA986900DF6D73 /* Config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Config.h; path = ../src/Config.h; sourceTree = SOURCE_ROOT; };
		C47E4C171CAA986900DF6D73 /* ConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ConfigSection.h; path = ../src/ConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C181CAA986900DF6D73 /* CRESTModel.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = CRESTModel.cpp; path = ../src/CRESTModel.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C191CAA986900DF6D73 /* CRESTModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CRESTModel.h; path = ../src/CRESTModel.h; sourceTree = SOURCE_ROOT; };
		C47E4C1A1CAA986900DF6D73 /* DamConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DamConfigSection.cpp; path = ../src/DamConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C1B1CAA986900DF6D73 /* DamConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DamConfigSection.h; path = ../src/DamConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C1C1CAA986900DF6D73 /* DatedName.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DatedName.cpp; path = ../src/DatedName.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C1D1CAA986900DF6D73 /* DatedName.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DatedName.h; path = ../src/DatedName.h; sourceTree = SOURCE_ROOT; };
		C47E4C1E1CAA986900DF6D73 /* Defines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Defines.h; path = ../src/Defines.h; sourceTree = SOURCE_ROOT; };
		C47E4C1F1CAA986900DF6D73 /* DEMProcessor.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DEMProcessor.cpp; path = ../src/DEMProcessor.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C201CAA986900DF6D73 /* DEMProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DEMProcessor.h; path = ../src/DEMProcessor.h; sourceTree = SOURCE_ROOT; };
		C47E4C211CAA986900DF6D73 /* DistancePerTimeUnits.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DistancePerTimeUnits.cpp; path = ../src/DistancePerTimeUnits.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C221CAA986900DF6D73 /* DistancePerTimeUnits.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DistancePerTimeUnits.h; path = ../src/DistancePerTimeUnits.h; sourceTree = SOURCE_ROOT; };
		C47E4C231CAA986900DF6D73 /* DistanceUnit.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DistanceUnit.cpp; path = ../src/DistanceUnit.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C241CAA986900DF6D73 /* DistanceUnit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DistanceUnit.h; path = ../src/DistanceUnit.h; sourceTree = SOURCE_ROOT; };
		C47E4C251CAA986900DF6D73 /* dream_functions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = dream_functions.cpp; path = ../src/dream_functions.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C261CAA986900DF6D73 /* dream_functions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dream_functions.h; path = ../src/dream_functions.h; sourceTree = SOURCE_ROOT; };
		C47E4C271CAA986900DF6D73 /* dream_variables.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = dream_variables.h; path = ../src/dream_variables.h; sourceTree = SOURCE_ROOT; };
		C47E4C281CAA986900DF6D73 /* DREAM.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DREAM.cpp; path = ../src/DREAM.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C291CAA986900DF6D73 /* DREAM.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DREAM.h; path = ../src/DREAM.h; sourceTree = SOURCE_ROOT; };
		C47E4C2A1CAA986900DF6D73 /* EF5.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = EF5.cpp; path = ../src/EF5.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C2B1CAA986900DF6D73 /* EF5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = EF5.h; path = ../src/EF5.h; sourceTree = SOURCE_ROOT; };
		C47E4C2F1CAA986900DF6D73 /* EnsTaskConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = EnsTaskConfigSection.cpp; path = ../src/EnsTaskConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C301CAA986900DF6D73 /* EnsTaskConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = EnsTaskConfigSection.h; path = ../src/EnsTaskConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C311CAA986900DF6D73 /* ExecuteConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = ExecuteConfigSection.cpp; path = ../src/ExecuteConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C321CAA986900DF6D73 /* ExecuteConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ExecuteConfigSection.h; path = ../src/ExecuteConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C331CAA986900DF6D73 /* ExecutionController.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = ExecutionController.cpp; path = ../src/ExecutionController.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C341CAA986900DF6D73 /* ExecutionController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ExecutionController.h; path = ../src/ExecutionController.h; sourceTree = SOURCE_ROOT; };
		C47E4C351CAA986900DF6D73 /* GaugeConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GaugeConfigSection.cpp; path = ../src/GaugeConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C361CAA986900DF6D73 /* GaugeConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GaugeConfigSection.h; path = ../src/GaugeConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C371CAA986900DF6D73 /* GaugeMap.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GaugeMap.cpp; path = ../src/GaugeMap.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C381CAA986900DF6D73 /* GaugeMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GaugeMap.h; path = ../src/GaugeMap.h; sourceTree = SOURCE_ROOT; };
		C47E4C391CAA986900DF6D73 /* GeographicProjection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GeographicProjection.cpp; path = ../src/GeographicProjection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C3A1CAA986900DF6D73 /* GeographicProjection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GeographicProjection.h; path = ../src/GeographicProjection.h; sourceTree = SOURCE_ROOT; };
		C47E4C3B1CAA986900DF6D73 /* Grid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Grid.h; path = ../src/Grid.h; sourceTree = SOURCE_ROOT; };
		C47E4C3C1CAA986900DF6D73 /* GriddedOutput.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GriddedOutput.cpp; path = ../src/GriddedOutput.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C3D1CAA986900DF6D73 /* GriddedOutput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GriddedOutput.h; path = ../src/GriddedOutput.h; sourceTree = SOURCE_ROOT; };
		C47E4C3E1CAA986900DF6D73 /* GridNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GridNode.h; path = ../src/GridNode.h; sourceTree = SOURCE_ROOT; };
		C47E4C3F1CAA986900DF6D73 /* GridWriter.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GridWriter.cpp; path = ../src/GridWriter.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C401CAA986900DF6D73 /* GridWriter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GridWriter.h; path = ../src/GridWriter.h; sourceTree = SOURCE_ROOT; };
		C47E4C411CAA986900DF6D73 /* GridWriterFull.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = GridWriterFull.cpp; path = ../src/GridWriterFull.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C421CAA986900DF6D73 /* GridWriterFull.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GridWriterFull.h; path = ../src/GridWriterFull.h; sourceTree = SOURCE_ROOT; };
		C47E4C431CAA986900DF6D73 /* HPModel.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = HPModel.cpp; path = ../src/HPModel.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C441CAA986900DF6D73 /* HPModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HPModel.h; path = ../src/HPModel.h; sourceTree = SOURCE_ROOT; };
		C47E4C451CAA986900DF6D73 /* HyMOD.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = HyMOD.cpp; path = ../src/HyMOD.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C461CAA986900DF6D73 /* HyMOD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HyMOD.h; path = ../src/HyMOD.h; sourceTree = SOURCE_ROOT; };
		C47E4C471CAA986900DF6D73 /* InundationCaliParamConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = InundationCaliParamConfigSection.cpp; path = ../src/InundationCaliParamConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C481CAA986900DF6D73 /* InundationCaliParamConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = InundationCaliParamConfigSection.h; path = ../src/InundationCaliParamConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C491CAA986900DF6D73 /* InundationParamSetConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = InundationParamSetConfigSection.cpp; path = ../src/InundationParamSetConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C4A1CAA986900DF6D73 /* InundationParamSetConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = InundationParamSetConfigSection.h; path = ../src/InundationParamSetConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C4B1CAA986900DF6D73 /* KinematicRoute.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = KinematicRoute.cpp; path = ../src/KinematicRoute.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C4C1CAA986900DF6D73 /* KinematicRoute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = KinematicRoute.h; path = ../src/KinematicRoute.h; sourceTree = SOURCE_ROOT; };
		C47E4C4E1CAA986900DF6D73 /* LAEAProjection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LAEAProjection.cpp; path = ../src/LAEAProjection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C4F1CAA986900DF6D73 /* LAEAProjection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LAEAProjection.h; path = ../src/LAEAProjection.h; sourceTree = SOURCE_ROOT; };
		C47E4C501CAA986900DF6D73 /* LinearRoute.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LinearRoute.cpp; path = ../src/LinearRoute.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C511CAA986900DF6D73 /* LinearRoute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LinearRoute.h; path = ../src/LinearRoute.h; sourceTree = SOURCE_ROOT; };
		C47E4C521CAA986900DF6D73 /* Messages.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Messages.h; path = ../src/Messages.h; sourceTree = SOURCE_ROOT; };
		C47E4C531CAA986900DF6D73 /* misc_functions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = misc_functions.cpp; path = ../src/misc_functions.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C541CAA986900DF6D73 /* misc_functions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = misc_functions.h; path = ../src/misc_functions.h; sourceTree = SOURCE_ROOT; };
		C47E4C551CAA986900DF6D73 /* Model.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Model.cpp; path = ../src/Model.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C561CAA986900DF6D73 /* Model.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Model.h; path = ../src/Model.h; sourceTree = SOURCE_ROOT; };
		C47E4C571CAA986900DF6D73 /* ModelBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ModelBase.h; path = ../src/ModelBase.h; sourceTree = SOURCE_ROOT; };
		C47E4C581CAA986900DF6D73 /* Models.tbl */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = Models.tbl; path = ../src/Models.tbl; sourceTree = SOURCE_ROOT; };
		C47E4C5A1CAA986900DF6D73 /* MRMSGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = MRMSGrid.cpp; path = ../src/MRMSGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C5B1CAA986900DF6D73 /* MRMSGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = MRMSGrid.h; path = ../src/MRMSGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C5D1CAA986900DF6D73 /* ObjectiveFunc.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = ObjectiveFunc.cpp; path = ../src/ObjectiveFunc.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C5E1CAA986900DF6D73 /* ObjectiveFunc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ObjectiveFunc.h; path = ../src/ObjectiveFunc.h; sourceTree = SOURCE_ROOT; };
		C47E4C5F1CAA986900DF6D73 /* ParamSetConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = ParamSetConfigSection.cpp; path = ../src/ParamSetConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C601CAA986900DF6D73 /* ParamSetConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ParamSetConfigSection.h; path = ../src/ParamSetConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C611CAA986900DF6D73 /* PETConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PETConfigSection.cpp; path = ../src/PETConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C621CAA986900DF6D73 /* PETConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PETConfigSection.h; path = ../src/PETConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C631CAA986900DF6D73 /* PETReader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PETReader.cpp; path = ../src/PETReader.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C641CAA986900DF6D73 /* PETReader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PETReader.h; path = ../src/PETReader.h; sourceTree = SOURCE_ROOT; };
		C47E4C651CAA986900DF6D73 /* PETType.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PETType.cpp; path = ../src/PETType.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C661CAA986900DF6D73 /* PETType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PETType.h; path = ../src/PETType.h; sourceTree = SOURCE_ROOT; };
		C47E4C671CAA986900DF6D73 /* PrecipConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PrecipConfigSection.cpp; path = ../src/PrecipConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C681CAA986900DF6D73 /* PrecipConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PrecipConfigSection.h; path = ../src/PrecipConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C691CAA986900DF6D73 /* PrecipReader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PrecipReader.cpp; path = ../src/PrecipReader.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C6A1CAA986900DF6D73 /* PrecipReader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PrecipReader.h; path = ../src/PrecipReader.h; sourceTree = SOURCE_ROOT; };
		C47E4C6B1CAA986900DF6D73 /* PrecipType.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PrecipType.cpp; path = ../src/PrecipType.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C6C1CAA986900DF6D73 /* PrecipType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PrecipType.h; path = ../src/PrecipType.h; sourceTree = SOURCE_ROOT; };
		C47E4C6D1CAA986900DF6D73 /* Projection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Projection.h; path = ../src/Projection.h; sourceTree = SOURCE_ROOT; };
		C47E4C6E1CAA986900DF6D73 /* RoutingCaliParamConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = RoutingCaliParamConfigSection.cpp; path = ../src/RoutingCaliParamConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C6F1CAA986900DF6D73 /* RoutingCaliParamConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RoutingCaliParamConfigSection.h; path = ../src/RoutingCaliParamConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C701CAA986900DF6D73 /* RoutingParamSetConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = RoutingParamSetConfigSection.cpp; path = ../src/RoutingParamSetConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C711CAA986900DF6D73 /* RoutingParamSetConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RoutingParamSetConfigSection.h; path = ../src/RoutingParamSetConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C721CAA986900DF6D73 /* RPSkewness.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = RPSkewness.cpp; path = ../src/RPSkewness.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C731CAA986900DF6D73 /* RPSkewness.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RPSkewness.h; path = ../src/RPSkewness.h; sourceTree = SOURCE_ROOT; };
		C47E4C741CAA986900DF6D73 /* SAC.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = SAC.cpp; path = ../src/SAC.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C751CAA986900DF6D73 /* SAC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SAC.h; path = ../src/SAC.h; sourceTree = SOURCE_ROOT; };
		C47E4C781CAA986900DF6D73 /* SimpleInundation.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = SimpleInundation.cpp; path = ../src/SimpleInundation.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C791CAA986900DF6D73 /* SimpleInundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SimpleInundation.h; path = ../src/SimpleInundation.h; sourceTree = SOURCE_ROOT; };
		C47E4C7A1CAA986900DF6D73 /* Simulator.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Simulator.cpp; path = ../src/Simulator.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C7B1CAA986900DF6D73 /* Simulator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Simulator.h; path = ../src/Simulator.h; sourceTree = SOURCE_ROOT; };
		C47E4C7C1CAA986900DF6D73 /* Snow17Model.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Snow17Model.cpp; path = ../src/Snow17Model.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C7D1CAA986900DF6D73 /* Snow17Model.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Snow17Model.h; path = ../src/Snow17Model.h; sourceTree = SOURCE_ROOT; };
		C47E4C7E1CAA986900DF6D73 /* SnowCaliParamConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = SnowCaliParamConfigSection.cpp; path = ../src/SnowCaliParamConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C7F1CAA986900DF6D73 /* SnowCaliParamConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SnowCaliParamConfigSection.h; path = ../src/SnowCaliParamConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C801CAA986900DF6D73 /* SnowParamSetConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = SnowParamSetConfigSection.cpp; path = ../src/SnowParamSetConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C811CAA986900DF6D73 /* SnowParamSetConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SnowParamSetConfigSection.h; path = ../src/SnowParamSetConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C821CAA986900DF6D73 /* TaskConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TaskConfigSection.cpp; path = ../src/TaskConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C831CAA986900DF6D73 /* TaskConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TaskConfigSection.h; path = ../src/TaskConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C841CAA986900DF6D73 /* TempConfigSection.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TempConfigSection.cpp; path = ../src/TempConfigSection.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C851CAA986900DF6D73 /* TempConfigSection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TempConfigSection.h; path = ../src/TempConfigSection.h; sourceTree = SOURCE_ROOT; };
		C47E4C861CAA986900DF6D73 /* TempReader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TempReader.cpp; path = ../src/TempReader.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C871CAA986900DF6D73 /* TempReader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TempReader.h; path = ../src/TempReader.h; sourceTree = SOURCE_ROOT; };
		C47E4C881CAA986900DF6D73 /* TempType.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TempType.cpp; path = ../src/TempType.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C891CAA986900DF6D73 /* TempType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TempType.h; path = ../src/TempType.h; sourceTree = SOURCE_ROOT; };
		C47E4C8A1CAA986900DF6D73 /* TifGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TifGrid.cpp; path = ../src/TifGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C8B1CAA986900DF6D73 /* TifGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TifGrid.h; path = ../src/TifGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C8C1CAA986900DF6D73 /* TimeSeries.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TimeSeries.cpp; path = ../src/TimeSeries.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C8D1CAA986900DF6D73 /* TimeSeries.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TimeSeries.h; path = ../src/TimeSeries.h; sourceTree = SOURCE_ROOT; };
		C47E4C8E1CAA986900DF6D73 /* TimeUnit.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TimeUnit.cpp; path = ../src/TimeUnit.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C8F1CAA986900DF6D73 /* TimeUnit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TimeUnit.h; path = ../src/TimeUnit.h; sourceTree = SOURCE_ROOT; };
		C47E4C901CAA986900DF6D73 /* TimeVar.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TimeVar.cpp; path = ../src/TimeVar.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C911CAA986900DF6D73 /* TimeVar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TimeVar.h; path = ../src/TimeVar.h; sourceTree = SOURCE_ROOT; };
		C47E4C931CAA986900DF6D73 /* TRMMDGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TRMMDGrid.cpp; path = ../src/TRMMDGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C941CAA986900DF6D73 /* TRMMDGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TRMMDGrid.h; path = ../src/TRMMDGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C961CAA986900DF6D73 /* TRMMRTGrid.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = TRMMRTGrid.cpp; path = ../src/TRMMRTGrid.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C971CAA986900DF6D73 /* TRMMRTGrid.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TRMMRTGrid.h; path = ../src/TRMMRTGrid.h; sourceTree = SOURCE_ROOT; };
		C47E4C9B1CAA986900DF6D73 /* VCInundation.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = VCInundation.cpp; path = ../src/VCInundation.cpp; sourceTree = SOURCE_ROOT; };
		C47E4C9C1CAA986900DF6D73 /* VCInundation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VCInundation.h; path = ../src/VCInundation.h; sourceTree = SOURCE_ROOT; };
		C47E4CE81CAAAB7100DF6D73 /* UnixImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UnixImageIO.framework; path = ../../../../../Library/Frameworks/UnixImageIO.framework; sourceTree = SOURCE_ROOT; };
		C47E4CEA1CAAAB8000DF6D73 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		C47E4BF61CAA984C00DF6D73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C47E4CEB1CAAAB8000DF6D73 /* libz.tbd in Frameworks */,
				C47E4CE91CAAAB7100DF6D73 /* UnixImageIO.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C47E4BF01CAA984C00DF6D73 = {
			isa = PBXGroup;
			children = (
				C47E4CEA1CAAAB8000DF6D73 /* libz.tbd */,
				C47E4CE81CAAAB7100DF6D73 /* UnixImageIO.framework */,
				C47E4BFB1CAA984C00DF6D73 /* EF5 */,
				C47E4BFA1CAA984C00DF6D73 /* Products */,
			);
			sourceTree = SOURCE_ROOT;
		};
		C47E4BFA1CAA984C00DF6D73 /* Products */ = {
			isa = PBXGroup;
			children = (
				C47E4BF91CAA984C00DF6D73 /* EF5 */,
			);
			name = Products;
			sourceTree = SOURCE_ROOT;
		};
		C47E4BFB1CAA984C00DF6D73 /* EF5 */ = {
			isa = PBXGroup;
			children = (
				C47E4D031CAD521F00DF6D73 /* Configs */,
				C47E4D021CAD521700DF6D73 /* Grids */,
				C47E4D011CAD51F900DF6D73 /* Calibration */,
				C47E4D041CAD52DA00DF6D73 /* Models */,
				C47E4C101CAA986900DF6D73 /* BoundingBox.h */,
				C47E4C111CAA986900DF6D73 /* Calibrate.h */,
				C47E4C1C1CAA986900DF6D73 /* DatedName.cpp */,
				C47E4C1D1CAA986900DF6D73 /* DatedName.h */,
				C47E4C1E1CAA986900DF6D73 /* Defines.h */,
				C47E4C1F1CAA986900DF6D73 /* DEMProcessor.cpp */,
				C47E4C201CAA986900DF6D73 /* DEMProcessor.h */,
				C47E4C211CAA986900DF6D73 /* DistancePerTimeUnits.cpp */,
				C47E4C221CAA986900DF6D73 /* DistancePerTimeUnits.h */,
				C47E4C231CAA986900DF6D73 /* DistanceUnit.cpp */,
				C47E4C241CAA986900DF6D73 /* DistanceUnit.h */,
				C47E4C2A1CAA986900DF6D73 /* EF5.cpp */,
				C47E4C2B1CAA986900DF6D73 /* EF5.h */,
				C47E4C331CAA986900DF6D73 /* ExecutionController.cpp */,
				C47E4C341CAA986900DF6D73 /* ExecutionController.h */,
				C47E4C371CAA986900DF6D73 /* GaugeMap.cpp */,
				C47E4C381CAA986900DF6D73 /* GaugeMap.h */,
				C47E4C391CAA986900DF6D73 /* GeographicProjection.cpp */,
				C47E4C3A1CAA986900DF6D73 /* GeographicProjection.h */,
				C47E4C3B1CAA986900DF6D73 /* Grid.h */,
				C47E4C3C1CAA986900DF6D73 /* GriddedOutput.cpp */,
				C47E4C3D1CAA986900DF6D73 /* GriddedOutput.h */,
				C47E4C3E1CAA986900DF6D73 /* GridNode.h */,
				C47E4C3F1CAA986900DF6D73 /* GridWriter.cpp */,
				C47E4C401CAA986900DF6D73 /* GridWriter.h */,
				C47E4C411CAA986900DF6D73 /* GridWriterFull.cpp */,
				C47E4C421CAA986900DF6D73 /* GridWriterFull.h */,
				C47E4C4E1CAA986900DF6D73 /* LAEAProjection.cpp */,
				C47E4C4F1CAA986900DF6D73 /* LAEAProjection.h */,
				C47E4C521CAA986900DF6D73 /* Messages.h */,
				C47E4C5D1CAA986900DF6D73 /* ObjectiveFunc.cpp */,
				C47E4C5E1CAA986900DF6D73 /* ObjectiveFunc.h */,
				C47E4C631CAA986900DF6D73 /* PETReader.cpp */,
				C47E4C641CAA986900DF6D73 /* PETReader.h */,
				C47E4C651CAA986900DF6D73 /* PETType.cpp */,
				C47E4C661CAA986900DF6D73 /* PETType.h */,
				C47E4C691CAA986900DF6D73 /* PrecipReader.cpp */,
				C47E4C6A1CAA986900DF6D73 /* PrecipReader.h */,
				C47E4C6B1CAA986900DF6D73 /* PrecipType.cpp */,
				C47E4C6C1CAA986900DF6D73 /* PrecipType.h */,
				C47E4C6D1CAA986900DF6D73 /* Projection.h */,
				C47E4C721CAA986900DF6D73 /* RPSkewness.cpp */,
				C47E4C731CAA986900DF6D73 /* RPSkewness.h */,
				C47E4C7A1CAA986900DF6D73 /* Simulator.cpp */,
				C47E4C7B1CAA986900DF6D73 /* Simulator.h */,
				C47E4C861CAA986900DF6D73 /* TempReader.cpp */,
				C47E4C871CAA986900DF6D73 /* TempReader.h */,
				C47E4C881CAA986900DF6D73 /* TempType.cpp */,
				C47E4C891CAA986900DF6D73 /* TempType.h */,
				C47E4C8C1CAA986900DF6D73 /* TimeSeries.cpp */,
				C47E4C8D1CAA986900DF6D73 /* TimeSeries.h */,
				C47E4C8E1CAA986900DF6D73 /* TimeUnit.cpp */,
				C47E4C8F1CAA986900DF6D73 /* TimeUnit.h */,
				C47E4C901CAA986900DF6D73 /* TimeVar.cpp */,
				C47E4C911CAA986900DF6D73 /* TimeVar.h */,
			);
			path = EF5;
			sourceTree = SOURCE_ROOT;
		};
		C47E4D011CAD51F900DF6D73 /* Calibration */ = {
			isa = PBXGroup;
			children = (
				C47E4C531CAA986900DF6D73 /* misc_functions.cpp */,
				C47E4C541CAA986900DF6D73 /* misc_functions.h */,
				C47E4C031CAA986900DF6D73 /* ARS.cpp */,
				C47E4C041CAA986900DF6D73 /* ARS.h */,
				C47E4C251CAA986900DF6D73 /* dream_functions.cpp */,
				C47E4C261CAA986900DF6D73 /* dream_functions.h */,
				C47E4C271CAA986900DF6D73 /* dream_variables.h */,
				C47E4C281CAA986900DF6D73 /* DREAM.cpp */,
				C47E4C291CAA986900DF6D73 /* DREAM.h */,
			);
			name = Calibration;
			sourceTree = SOURCE_ROOT;
		};
		C47E4D021CAD521700DF6D73 /* Grids */ = {
			isa = PBXGroup;
			children = (
				C47E4C051CAA986900DF6D73 /* AscGrid.cpp */,
				C47E4C061CAA986900DF6D73 /* AscGrid.h */,
				C47E4C091CAA986900DF6D73 /* BasicGrids.cpp */,
				C47E4C0A1CAA986900DF6D73 /* BasicGrids.h */,
				C47E4C0E1CAA986900DF6D73 /* BifGrid.cpp */,
				C47E4C0F1CAA986900DF6D73 /* BifGrid.h */,
				C47E4C5A1CAA986900DF6D73 /* MRMSGrid.cpp */,
				C47E4C5B1CAA986900DF6D73 /* MRMSGrid.h */,
				C47E4C8A1CAA986900DF6D73 /* TifGrid.cpp */,
				C47E4C8B1CAA986900DF6D73 /* TifGrid.h */,
				C47E4C931CAA986900DF6D73 /* TRMMDGrid.cpp */,
				C47E4C941CAA986900DF6D73 /* TRMMDGrid.h */,
				C47E4C961CAA986900DF6D73 /* TRMMRTGrid.cpp */,
				C47E4C971CAA986900DF6D73 /* TRMMRTGrid.h */,
			);
			name = Grids;
			sourceTree = SOURCE_ROOT;
		};
		C47E4D031CAD521F00DF6D73 /* Configs */ = {
			isa = PBXGroup;
			children = (
				C47E4C2F1CAA986900DF6D73 /* EnsTaskConfigSection.cpp */,
				C47E4C301CAA986900DF6D73 /* EnsTaskConfigSection.h */,
				C47E4C311CAA986900DF6D73 /* ExecuteConfigSection.cpp */,
				C47E4C321CAA986900DF6D73 /* ExecuteConfigSection.h */,
				C47E4C0B1CAA986900DF6D73 /* BasinConfigSection.cpp */,
				C47E4C0C1CAA986900DF6D73 /* BasinConfigSection.h */,
				C47E4C121CAA986900DF6D73 /* CaliParamConfigSection.cpp */,
				C47E4C131CAA986900DF6D73 /* CaliParamConfigSection.h */,
				C47E4C1A1CAA986900DF6D73 /* DamConfigSection.cpp */,
				C47E4C1B1CAA986900DF6D73 /* DamConfigSection.h */,
				C47E4C151CAA986900DF6D73 /* Config.cpp */,
				C47E4C161CAA986900DF6D73 /* Config.h */,
				C47E4C171CAA986900DF6D73 /* ConfigSection.h */,
				C47E4C071CAA986900DF6D73 /* BasicConfigSection.cpp */,
				C47E4C081CAA986900DF6D73 /* BasicConfigSection.h */,
				C47E4C351CAA986900DF6D73 /* GaugeConfigSection.cpp */,
				C47E4C361CAA986900DF6D73 /* GaugeConfigSection.h */,
				C47E4C471CAA986900DF6D73 /* InundationCaliParamConfigSection.cpp */,
				C47E4C481CAA986900DF6D73 /* InundationCaliParamConfigSection.h */,
				C47E4C491CAA986900DF6D73 /* InundationParamSetConfigSection.cpp */,
				C47E4C4A1CAA986900DF6D73 /* InundationParamSetConfigSection.h */,
				C47E4C6E1CAA986900DF6D73 /* RoutingCaliParamConfigSection.cpp */,
				C47E4C6F1CAA986900DF6D73 /* RoutingCaliParamConfigSection.h */,
				C47E4C701CAA986900DF6D73 /* RoutingParamSetConfigSection.cpp */,
				C47E4C711CAA986900DF6D73 /* RoutingParamSetConfigSection.h */,
				C47E4C5F1CAA986900DF6D73 /* ParamSetConfigSection.cpp */,
				C47E4C601CAA986900DF6D73 /* ParamSetConfigSection.h */,
				C47E4C611CAA986900DF6D73 /* PETConfigSection.cpp */,
				C47E4C621CAA986900DF6D73 /* PETConfigSection.h */,
				C47E4C7E1CAA986900DF6D73 /* SnowCaliParamConfigSection.cpp */,
				C47E4C7F1CAA986900DF6D73 /* SnowCaliParamConfigSection.h */,
				C47E4C801CAA986900DF6D73 /* SnowParamSetConfigSection.cpp */,
				C47E4C811CAA986900DF6D73 /* SnowParamSetConfigSection.h */,
				C47E4C821CAA986900DF6D73 /* TaskConfigSection.cpp */,
				C47E4C831CAA986900DF6D73 /* TaskConfigSection.h */,
				C47E4C841CAA986900DF6D73 /* TempConfigSection.cpp */,
				C47E4C851CAA986900DF6D73 /* TempConfigSection.h */,
				C47E4C671CAA986900DF6D73 /* PrecipConfigSection.cpp */,
				C47E4C681CAA986900DF6D73 /* PrecipConfigSection.h */,
			);
			name = Configs;
			sourceTree = SOURCE_ROOT;
		};
		C47E4D041CAD52DA00DF6D73 /* Models */ = {
			isa = PBXGroup;
			children = (
				C47E4C181CAA986900DF6D73 /* CRESTModel.cpp */,
				C47E4C191CAA986900DF6D73 /* CRESTModel.h */,
				C47E4C431CAA986900DF6D73 /* HPModel.cpp */,
				C47E4C441CAA986900DF6D73 /* HPModel.h */,
				C47E4C451CAA986900DF6D73 /* HyMOD.cpp */,
				C47E4C461CAA986900DF6D73 /* HyMOD.h */,
				C47E4C4B1CAA986900DF6D73 /* KinematicRoute.cpp */,
				C47E4C4C1CAA986900DF6D73 /* KinematicRoute.h */,
				C47E4C501CAA986900DF6D73 /* LinearRoute.cpp */,
				C47E4C511CAA986900DF6D73 /* LinearRoute.h */,
				C47E4C551CAA986900DF6D73 /* Model.cpp */,
				C47E4C561CAA986900DF6D73 /* Model.h */,
				C47E4C571CAA986900DF6D73 /* ModelBase.h */,
				C47E4C581CAA986900DF6D73 /* Models.tbl */,
				C47E4C741CAA986900DF6D73 /* SAC.cpp */,
				C47E4C751CAA986900DF6D73 /* SAC.h */,
				C47E4C781CAA986900DF6D73 /* SimpleInundation.cpp */,
				C47E4C791CAA986900DF6D73 /* SimpleInundation.h */,
				C47E4C7C1CAA986900DF6D73 /* Snow17Model.cpp */,
				C47E4C7D1CAA986900DF6D73 /* Snow17Model.h */,
				C47E4C9B1CAA986900DF6D73 /* VCInundation.cpp */,
				C47E4C9C1CAA986900DF6D73 /* VCInundation.h */,
			);
			name = Models;
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C47E4BF81CAA984C00DF6D73 /* EF5 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C47E4C001CAA984C00DF6D73 /* Build configuration list for PBXNativeTarget "EF5" */;
			buildPhases = (
				C47E4BF51CAA984C00DF6D73 /* Sources */,
				C47E4BF61CAA984C00DF6D73 /* Frameworks */,
				C47E4BF71CAA984C00DF6D73 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = EF5;
			productName = EF5;
			productReference = C47E4BF91CAA984C00DF6D73 /* EF5 */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C47E4BF11CAA984C00DF6D73 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0910;
				ORGANIZATIONNAME = "University of Oklahoma";
				TargetAttributes = {
					C47E4BF81CAA984C00DF6D73 = {
						CreatedOnToolsVersion = 7.2.1;
					};
				};
			};
			buildConfigurationList = C47E4BF41CAA984C00DF6D73 /* Build configuration list for PBXProject "EF5" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = C47E4BF01CAA984C00DF6D73;
			productRefGroup = C47E4BFA1CAA984C00DF6D73 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C47E4BF81CAA984C00DF6D73 /* EF5 */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		C47E4BF51CAA984C00DF6D73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C47E4CD91CAA986900DF6D73 /* TaskConfigSection.cpp in Sources */,
				C47E4CCD1CAA986900DF6D73 /* PrecipReader.cpp in Sources */,
				C47E4CCB1CAA986900DF6D73 /* PETType.cpp in Sources */,
				C47E4CA41CAA986900DF6D73 /* CaliParamConfigSection.cpp in Sources */,
				C47E4CC21CAA986900DF6D73 /* misc_functions.cpp in Sources */,
				C47E4CE21CAA986900DF6D73 /* TRMMDGrid.cpp in Sources */,
				C47E4CE71CAA986900DF6D73 /* VCInundation.cpp in Sources */,
				C47E4CBE1CAA986900DF6D73 /* KinematicRoute.cpp in Sources */,
				C47E4CDE1CAA986900DF6D73 /* TimeSeries.cpp in Sources */,
				C47E4CB61CAA986900DF6D73 /* GeographicProjection.cpp in Sources */,
				C47E4CCC1CAA986900DF6D73 /* PrecipConfigSection.cpp in Sources */,
				C47E4CC01CAA986900DF6D73 /* LAEAProjection.cpp in Sources */,
				C47E4CB81CAA986900DF6D73 /* GridWriter.cpp in Sources */,
				C47E4CAA1CAA986900DF6D73 /* DEMProcessor.cpp in Sources */,
				C47E4C9F1CAA986900DF6D73 /* BasicConfigSection.cpp in Sources */,
				C47E4CCA1CAA986900DF6D73 /* PETReader.cpp in Sources */,
				C47E4CDD1CAA986900DF6D73 /* TifGrid.cpp in Sources */,
				C47E4CAE1CAA986900DF6D73 /* DREAM.cpp in Sources */,
				C47E4CCE1CAA986900DF6D73 /* PrecipType.cpp in Sources */,
				C47E4CAF1CAA986900DF6D73 /* EF5.cpp in Sources */,
				C47E4CA61CAA986900DF6D73 /* Config.cpp in Sources */,
				C47E4CA71CAA986900DF6D73 /* CRESTModel.cpp in Sources */,
				C47E4CA31CAA986900DF6D73 /* BifGrid.cpp in Sources */,
				C47E4CE01CAA986900DF6D73 /* TimeVar.cpp in Sources */,
				C47E4CCF1CAA986900DF6D73 /* RoutingCaliParamConfigSection.cpp in Sources */,
				C47E4CA81CAA986900DF6D73 /* DamConfigSection.cpp in Sources */,
				C47E4CD41CAA986900DF6D73 /* SimpleInundation.cpp in Sources */,
				C47E4CD81CAA986900DF6D73 /* SnowParamSetConfigSection.cpp in Sources */,
				C47E4CB41CAA986900DF6D73 /* GaugeConfigSection.cpp in Sources */,
				C47E4CC91CAA986900DF6D73 /* PETConfigSection.cpp in Sources */,
				C47E4CD21CAA986900DF6D73 /* SAC.cpp in Sources */,
				C47E4CC71CAA986900DF6D73 /* ObjectiveFunc.cpp in Sources */,
				C47E4CB91CAA986900DF6D73 /* GridWriterFull.cpp in Sources */,
				C47E4CAD1CAA986900DF6D73 /* dream_functions.cpp in Sources */,
				C47E4CAB1CAA986900DF6D73 /* DistancePerTimeUnits.cpp in Sources */,
				C47E4CDA1CAA986900DF6D73 /* TempConfigSection.cpp in Sources */,
				C47E4CA11CAA986900DF6D73 /* BasinConfigSection.cpp in Sources */,
				C47E4CBC1CAA986900DF6D73 /* InundationCaliParamConfigSection.cpp in Sources */,
				C47E4CD01CAA986900DF6D73 /* RoutingParamSetConfigSection.cpp in Sources */,
				C47E4CD51CAA986900DF6D73 /* Simulator.cpp in Sources */,
				C47E4CBD1CAA986900DF6D73 /* InundationParamSetConfigSection.cpp in Sources */,
				C47E4CC11CAA986900DF6D73 /* LinearRoute.cpp in Sources */,
				C47E4CD11CAA986900DF6D73 /* RPSkewness.cpp in Sources */,
				C47E4C9D1CAA986900DF6D73 /* ARS.cpp in Sources */,
				C47E4CC31CAA986900DF6D73 /* Model.cpp in Sources */,
				C47E4CB71CAA986900DF6D73 /* GriddedOutput.cpp in Sources */,
				C47E4CAC1CAA986900DF6D73 /* DistanceUnit.cpp in Sources */,
				C47E4CB11CAA986900DF6D73 /* EnsTaskConfigSection.cpp in Sources */,
				C47E4CC81CAA986900DF6D73 /* ParamSetConfigSection.cpp in Sources */,
				C47E4CD61CAA986900DF6D73 /* Snow17Model.cpp in Sources */,
				C47E4CB31CAA986900DF6D73 /* ExecutionController.cpp in Sources */,
				C47E4CDC1CAA986900DF6D73 /* TempType.cpp in Sources */,
				C47E4CA01CAA986900DF6D73 /* BasicGrids.cpp in Sources */,
				C47E4CBA1CAA986900DF6D73 /* HPModel.cpp in Sources */,
				C47E4CB21CAA986900DF6D73 /* ExecuteConfigSection.cpp in Sources */,
				C47E4CDB1CAA986900DF6D73 /* TempReader.cpp in Sources */,
				C47E4CD71CAA986900DF6D73 /* SnowCaliParamConfigSection.cpp in Sources */,
				C47E4CB51CAA986900DF6D73 /* GaugeMap.cpp in Sources */,
				C47E4CA91CAA986900DF6D73 /* DatedName.cpp in Sources */,
				C47E4CE41CAA986900DF6D73 /* TRMMRTGrid.cpp in Sources */,
				C47E4CC51CAA986900DF6D73 /* MRMSGrid.cpp in Sources */,
				C47E4C9E1CAA986900DF6D73 /* AscGrid.cpp in Sources */,
				C47E4CDF1CAA986900DF6D73 /* TimeUnit.cpp in Sources */,
				C47E4CBB1CAA986900DF6D73 /* HyMOD.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		C47E4BFE1CAA984C00DF6D73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		C47E4BFF1CAA984C00DF6D73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		C47E4C011CAA984C00DF6D73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(LOCAL_LIBRARY_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = /Library/Frameworks/UnixImageIO.framework/Headers;
				OTHER_CFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		C47E4C021CAA984C00DF6D73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(LOCAL_LIBRARY_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = /Library/Frameworks/UnixImageIO.framework/Headers;
				OTHER_CFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C47E4BF41CAA984C00DF6D73 /* Build configuration list for PBXProject "EF5" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C47E4BFE1CAA984C00DF6D73 /* Debug */,
				C47E4BFF1CAA984C00DF6D73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C47E4C001CAA984C00DF6D73 /* Build configuration list for PBXNativeTarget "EF5" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C47E4C011CAA984C00DF6D73 /* Debug */,
				C47E4C021CAA984C00DF6D73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C47E4BF11CAA984C00DF6D73 /* Project object */;
}
