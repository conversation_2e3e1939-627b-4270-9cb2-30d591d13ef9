[Basic]

DEM=basic\dem.tif

DDM=basic\ddm.tif

FAM=basic\fac.tif

PROJ=geographic
ESRIDDM=false
SelfFAM=true





[PrecipForcing TRMM]

TYPE=BIF

UNIT=mm/d

FREQ=d
LOC=precip\

NAME=TR_YYYYMMDD.bif



[PETForcing FEWSNET]

TYPE=BIF

UNIT=mm/d

FREQ=m

LOC=pet\

NAME=PET025.MM.bif



[Gauge <PERSON>hu<PERSON>]

LON=89.530485

LAT=27.108927
OBS=obs\chhukha.csv

BASINAREA=4023.00
OUTPUTTS=TRUE



[Basin Wangchu]

GAUGE=Chhukha



# CRESTPHYS模型校准参数配置 (用于DREAM校准)
[crestphyscaliparams CRESTPHYS_CALI]
GAUGE=Chhukha
OBJECTIVE=NSCE
DREAM_NDRAW=1000
# 参数格式: 最小值,最大值,初始值
WM=20.0,200.0,75.403
B=1.0,20.0,13.204
IM=0.01,10.0,0.154
KE=0.1,2.0,0.362
FC=1.0,100.0,53.558
KSOIL=0.01,0.5,0.1
IWU=10.0,90.0,24.999
IGW=10.0,90.0,25.0
HMAXAQ=0.5,5.0,1.0
GWC=0.5,2.0,1.0
GWE=0.5,2.0,1.0

# KW汇流校准参数配置 (用于DREAM校准)
[kwcaliparams KW_CALI]
GAUGE=Chhukha
UNDER=0.5,10.0,2.976
LEAKI=0.001,0.2,0.042
TH=1.0,15.0,4.031
ISU=0.0,1.0,0.0
ALPHA=1.0,10.0,2.847
BETA=0.5,1.5,0.884
ALPHA0=0.5,10.0,1.174

# CRESTPHYS模型参数配置 (用于模拟运行)
[crestphysparamset Wangchu]
gauge=Chhukha
wm=75.403
b=13.204
im=0.154
ke=0.362
fc=53.558
ksoil=0.1
iwu=24.999
igw=25.0
hmaxaq=1.0
gwc=1.0
gwe=1.0

# KW汇流参数配置 (用于模拟运行)
[kwparamset Wangchu]
gauge=Chhukha
under=2.976
leaki=0.042
th=4.031
isu=0.000000
alpha=2.847
beta=0.884
alpha0=1.174

# DREAM校准任务
[Task CalibrateWangchu]
STYLE=CALI_DREAM
MODEL=CRESTPHYS
ROUTING=KW
BASIN=Wangchu
PRECIP=TRMM
PET=FEWSNET
OUTPUT=output\calibration\
PARAM_SET=Wangchu
ROUTING_PARAM_SET=Wangchu
CALI_PARAM=CRESTPHYS_CALI
ROUTING_CALI_PARAM=KW_CALI
TIMESTEP=1d
TIME_BEGIN=************
TIME_WARMEND=************
TIME_END=************

# 模拟运行任务 (使用校准后的参数)
[Task RunWangchu]
STYLE=SIMU
MODEL=CRESTPHYS
ROUTING=KW
BASIN=Wangchu
PRECIP=TRMM
PET=FEWSNET
OUTPUT=output\
PARAM_SET=Wangchu
ROUTING_PARAM_SET=Wangchu
TIMESTEP=1d
TIME_BEGIN=************
TIME_WARMEND=************
TIME_END=************



[Execute]
# 执行DREAM校准任务
TASK=CalibrateWangchu
# 如果要执行模拟运行，请注释上面一行，取消注释下面一行
#TASK=RunWangchu
