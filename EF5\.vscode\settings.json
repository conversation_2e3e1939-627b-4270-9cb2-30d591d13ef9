{"files.associations": {"*.tbl": "cpp", "defines.h": "c", "configsection.h": "c", "model.h": "c", "modelbase.h": "c", "basicgrids.h": "c", "cctype": "c", "clocale": "c", "cmath": "c", "cstdarg": "c", "cstddef": "c", "cstdio": "c", "cstdlib": "c", "cstring": "c", "ctime": "c", "cwchar": "c", "cwctype": "c", "array": "c", "atomic": "c", "strstream": "c", "bit": "c", "*.tcc": "c", "bitset": "c", "chrono": "c", "condition_variable": "c", "cstdint": "c", "deque": "c", "list": "c", "map": "c", "set": "c", "unordered_map": "c", "vector": "c", "exception": "c", "algorithm": "c", "functional": "c", "iterator": "c", "memory": "c", "memory_resource": "c", "numeric": "c", "optional": "c", "random": "c", "ratio": "c", "string": "c", "string_view": "c", "system_error": "c", "tuple": "c", "type_traits": "c", "utility": "c", "fstream": "c", "initializer_list": "c", "iosfwd": "c", "iostream": "c", "istream": "c", "limits": "c", "mutex": "c", "new": "c", "ostream": "c", "shared_mutex": "c", "sstream": "c", "stdexcept": "c", "streambuf": "c", "thread": "c", "cinttypes": "c", "typeinfo": "c"}}